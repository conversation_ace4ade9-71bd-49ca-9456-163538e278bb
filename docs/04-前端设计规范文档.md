# 多语言多域名宠物博客管理平台 - 前端设计规范文档

## 1. 设计理念与原则

### 1.1 核心设计理念
- **内容优先**：以优质内容展示为核心，界面设计服务于内容
- **SEO驱动**：所有设计决策都考虑SEO影响
- **性能至上**：追求极致的加载速度和响应性能
- **无障碍设计**：确保所有用户都能访问和使用

### 1.2 设计原则
1. **简洁清晰**：减少视觉噪音，突出核心内容
2. **响应式设计**：移动优先，适配所有设备
3. **一致性**：保持视觉和交互的一致性
4. **可读性**：优化文字排版和对比度
5. **渐进增强**：基础功能优先，高级特性渐进添加

## 2. 品牌设计规范

### 2.1 色彩系统

#### 主色调
```css
:root {
  /* 品牌主色 */
  --primary-color: #2563eb;        /* 蓝色 - 信任、专业 */
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  
  /* 辅助色 */
  --secondary-color: #10b981;      /* 绿色 - 健康、自然 */
  --accent-color: #f59e0b;         /* 橙色 - 活力、友好 */
  
  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 语义色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
}
```

#### 暗色模式
```css
[data-theme="dark"] {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
}
```

### 2.2 字体系统

#### 字体栈
```css
:root {
  /* 西文字体 */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-serif: 'Merriweather', Georgia, serif;
  --font-mono: 'Fira Code', 'Consolas', monospace;
  
  /* 中文字体 */
  --font-chinese: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  
  /* 字体大小 */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  
  /* 字重 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

### 2.3 间距系统
```css
:root {
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
}
```

## 3. 响应式设计规范

### 3.1 断点系统
```css
/* 移动优先断点 */
--breakpoint-sm: 640px;   /* 手机横屏 */
--breakpoint-md: 768px;   /* 平板竖屏 */
--breakpoint-lg: 1024px;  /* 平板横屏/小笔记本 */
--breakpoint-xl: 1280px;  /* 桌面显示器 */
--breakpoint-2xl: 1536px; /* 大屏显示器 */
```

### 3.2 网格系统
```css
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container { max-width: 640px; }
}

@media (min-width: 768px) {
  .container { max-width: 768px; }
}

@media (min-width: 1024px) {
  .container { max-width: 1024px; }
}

@media (min-width: 1280px) {
  .container { max-width: 1280px; }
}
```

### 3.3 响应式组件设计原则
1. **流式布局**：使用相对单位（%、rem、vw）
2. **弹性图片**：`max-width: 100%; height: auto;`
3. **触摸友好**：最小点击区域 44x44px
4. **隐藏/显示**：合理使用响应式工具类

## 4. UI组件规范

### 4.1 按钮组件
```html
<!-- 主要按钮 -->
<button class="btn btn-primary">
  阅读更多
</button>

<!-- 次要按钮 -->
<button class="btn btn-secondary">
  分享文章
</button>

<!-- 文字按钮 -->
<button class="btn btn-text">
  查看全部
</button>
```

#### 按钮样式
```css
.btn {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
}

.btn-primary:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}
```

### 4.2 卡片组件
```html
<article class="card">
  <img class="card-image" src="..." alt="..." loading="lazy">
  <div class="card-body">
    <span class="card-category">狗狗品种</span>
    <h3 class="card-title">金毛寻回犬饲养指南</h3>
    <p class="card-excerpt">金毛寻回犬是最受欢迎的家庭犬之一...</p>
    <div class="card-meta">
      <time>2024年1月1日</time>
      <span>阅读 1,234</span>
    </div>
  </div>
</article>
```

### 4.3 导航组件
```html
<nav class="nav-primary">
  <div class="container">
    <a href="/" class="nav-logo">
      <img src="/logo.svg" alt="宠物博客">
    </a>
    
    <ul class="nav-menu">
      <li><a href="/dogs" class="nav-link">狗狗</a></li>
      <li><a href="/cats" class="nav-link">猫咪</a></li>
      <li><a href="/about" class="nav-link">关于我们</a></li>
    </ul>
    
    <div class="nav-actions">
      <button class="nav-search" aria-label="搜索">
        <svg>...</svg>
      </button>
      <button class="nav-menu-toggle" aria-label="菜单">
        <svg>...</svg>
      </button>
    </div>
  </div>
</nav>
```

### 4.4 表单组件
```html
<!-- 搜索表单 -->
<form class="search-form" role="search">
  <label for="search" class="sr-only">搜索</label>
  <input 
    type="search" 
    id="search"
    class="search-input" 
    placeholder="搜索文章..."
    aria-label="搜索文章"
  >
  <button type="submit" class="search-button">
    <svg>...</svg>
    <span class="sr-only">搜索</span>
  </button>
</form>

<!-- 评论表单 -->
<form class="comment-form">
  <div class="form-group">
    <label for="author">名称 *</label>
    <input type="text" id="author" name="author" required>
  </div>
  
  <div class="form-group">
    <label for="email">邮箱 *</label>
    <input type="email" id="email" name="email" required>
  </div>
  
  <div class="form-group">
    <label for="comment">评论内容 *</label>
    <textarea id="comment" name="comment" rows="4" required></textarea>
  </div>
  
  <button type="submit" class="btn btn-primary">
    提交评论
  </button>
</form>
```

## 5. SEO优化规范

### 5.1 HTML结构优化
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <!-- SEO Meta Tags -->
  <title>金毛寻回犬饲养指南 - 宠物博客</title>
  <meta name="description" content="详细介绍金毛寻回犬的饲养方法...">
  <meta name="keywords" content="金毛,寻回犬,饲养">
  
  <!-- Open Graph -->
  <meta property="og:title" content="金毛寻回犬饲养指南">
  <meta property="og:description" content="详细介绍金毛寻回犬的饲养方法...">
  <meta property="og:image" content="https://example.com/golden.jpg">
  <meta property="og:url" content="https://example.com/dogs/golden-retriever">
  <meta property="og:type" content="article">
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="金毛寻回犬饲养指南">
  <meta name="twitter:description" content="详细介绍金毛寻回犬的饲养方法...">
  <meta name="twitter:image" content="https://example.com/golden.jpg">
  
  <!-- Canonical URL -->
  <link rel="canonical" href="https://example.com/dogs/golden-retriever">
  
  <!-- Hreflang -->
  <link rel="alternate" hreflang="en" href="https://example.com/dogs/golden-retriever">
  <link rel="alternate" hreflang="de" href="https://example.de/hunde/golden-retriever">
  <link rel="alternate" hreflang="ru" href="https://example.ru/sobaki/golden-retriver">
  
  <!-- 结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "金毛寻回犬饲养指南",
    "image": "https://example.com/golden.jpg",
    "datePublished": "2024-01-01",
    "dateModified": "2024-01-01",
    "author": {
      "@type": "Organization",
      "name": "宠物博客"
    }
  }
  </script>
</head>
```

### 5.2 语义化标签使用
```html
<article>
  <header>
    <h1>金毛寻回犬饲养指南</h1>
    <div class="article-meta">
      <time datetime="2024-01-01">2024年1月1日</time>
      <span>作者：管理员</span>
    </div>
  </header>
  
  <nav aria-label="目录">
    <h2>文章目录</h2>
    <ol>
      <li><a href="#breed-intro">品种介绍</a></li>
      <li><a href="#care-guide">饲养指南</a></li>
    </ol>
  </nav>
  
  <main>
    <section id="breed-intro">
      <h2>品种介绍</h2>
      <p>金毛寻回犬...</p>
    </section>
    
    <section id="care-guide">
      <h2>饲养指南</h2>
      <p>日常护理...</p>
    </section>
  </main>
  
  <footer>
    <section class="related-articles">
      <h3>相关文章</h3>
      <ul>...</ul>
    </section>
  </footer>
</article>
```

### 5.3 面包屑导航
```html
<nav aria-label="面包屑">
  <ol class="breadcrumb" itemscope itemtype="https://schema.org/BreadcrumbList">
    <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
      <a itemprop="item" href="/">
        <span itemprop="name">首页</span>
      </a>
      <meta itemprop="position" content="1" />
    </li>
    <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
      <a itemprop="item" href="/dogs">
        <span itemprop="name">狗狗</span>
      </a>
      <meta itemprop="position" content="2" />
    </li>
    <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
      <span itemprop="name">金毛寻回犬饲养指南</span>
      <meta itemprop="position" content="3" />
    </li>
  </ol>
</nav>
```

## 6. 性能优化规范

### 6.1 图片优化
```html
<!-- 响应式图片 -->
<picture>
  <source 
    type="image/webp" 
    srcset="golden-small.webp 640w, 
            golden-medium.webp 1024w, 
            golden-large.webp 1920w"
    sizes="(max-width: 640px) 100vw, 
           (max-width: 1024px) 50vw, 
           33vw">
  <img 
    src="golden-medium.jpg" 
    alt="金毛寻回犬"
    loading="lazy"
    decoding="async"
    width="1024"
    height="768">
</picture>
```

### 6.2 Critical CSS
```html
<style>
  /* 内联关键CSS */
  :root { /* 变量定义 */ }
  body { /* 基础样式 */ }
  .header { /* 头部样式 */ }
  .hero { /* 首屏样式 */ }
</style>

<!-- 异步加载非关键CSS -->
<link rel="preload" href="/css/main.css" as="style">
<link rel="stylesheet" href="/css/main.css" media="print" onload="this.media='all'">
```

### 6.3 JavaScript优化
```html
<!-- 延迟加载非关键脚本 -->
<script src="/js/main.js" defer></script>

<!-- 模块化加载 -->
<script type="module">
  import { initSearch } from './modules/search.js';
  import { initComments } from './modules/comments.js';
  
  // 按需初始化
  if (document.querySelector('.search-form')) {
    initSearch();
  }
  
  if (document.querySelector('.comment-form')) {
    initComments();
  }
</script>
```

## 7. 无障碍设计规范

### 7.1 ARIA标签使用
```html
<!-- 导航 -->
<nav role="navigation" aria-label="主导航">
  <ul role="menubar">
    <li role="none">
      <a role="menuitem" href="/dogs">狗狗</a>
    </li>
  </ul>
</nav>

<!-- 搜索 -->
<form role="search">
  <input type="search" aria-label="搜索文章">
  <button type="submit" aria-label="执行搜索">
    <svg aria-hidden="true">...</svg>
  </button>
</form>

<!-- 加载状态 -->
<div role="status" aria-live="polite" aria-busy="true">
  <span class="sr-only">加载中...</span>
</div>
```

### 7.2 键盘导航
```css
/* 焦点样式 */
:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 跳转链接 */
.skip-link {
  position: absolute;
  left: -9999px;
}

.skip-link:focus {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
}
```

### 7.3 颜色对比度
- 正文文字：至少 4.5:1
- 大号文字：至少 3:1
- 交互元素：至少 3:1

## 8. 动画与过渡规范

### 8.1 过渡效果
```css
/* 标准过渡 */
.transition-all {
  transition: all 0.2s ease;
}

.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease;
}

.transition-transform {
  transition: transform 0.2s ease;
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}
```

### 8.2 加载动画
```css
.skeleton {
  background: linear-gradient(90deg, 
    var(--gray-200) 25%, 
    var(--gray-300) 50%, 
    var(--gray-200) 75%
  );
  background-size: 200% 100%;
  animation: skeleton 1.5s ease-in-out infinite;
}

@keyframes skeleton {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
```

## 9. 多语言适配规范

### 9.1 文字方向处理
```css
/* RTL语言支持 */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .card {
  direction: rtl;
}

[dir="rtl"] .nav-menu {
  flex-direction: row-reverse;
}
```

### 9.2 字体适配
```css
/* 语言特定字体 */
:lang(en) {
  font-family: var(--font-sans);
}

:lang(de) {
  font-family: var(--font-sans);
}

:lang(ru) {
  font-family: 'Noto Sans', var(--font-sans);
}

:lang(zh) {
  font-family: var(--font-chinese);
}
```

## 10. 组件使用示例

### 10.1 文章列表页
```html
<main class="article-list">
  <header class="page-header">
    <h1>狗狗品种介绍</h1>
    <p>了解各种狗狗品种的特点和饲养方法</p>
  </header>
  
  <div class="article-grid">
    <article class="card">
      <!-- 文章卡片内容 -->
    </article>
    <!-- 更多文章卡片 -->
  </div>
  
  <nav class="pagination" aria-label="分页">
    <a href="?page=1" class="pagination-prev" aria-label="上一页">
      <svg>...</svg>
    </a>
    <span class="pagination-current">第 2 页，共 10 页</span>
    <a href="?page=3" class="pagination-next" aria-label="下一页">
      <svg>...</svg>
    </a>
  </nav>
</main>
```

### 10.2 文章详情页
```html
<article class="article-detail">
  <header class="article-header">
    <nav class="breadcrumb"><!-- 面包屑 --></nav>
    <h1 class="article-title">金毛寻回犬饲养指南</h1>
    <div class="article-meta">
      <time>2024年1月1日</time>
      <span>5分钟阅读</span>
    </div>
  </header>
  
  <aside class="article-toc">
    <h2>目录</h2>
    <nav><!-- 文章目录 --></nav>
  </aside>
  
  <div class="article-content">
    <!-- 文章内容 -->
  </div>
  
  <footer class="article-footer">
    <section class="article-tags">
      <h3>标签</h3>
      <ul><!-- 标签列表 --></ul>
    </section>
    
    <section class="article-share">
      <h3>分享</h3>
      <!-- 分享按钮 -->
    </section>
    
    <section class="related-articles">
      <h3>相关文章</h3>
      <!-- 相关文章列表 -->
    </section>
  </footer>
</article>
```

## 11. 测试规范

### 11.1 浏览器兼容性
- Chrome (最新2个版本)
- Firefox (最新2个版本)
- Safari (最新2个版本)
- Edge (最新2个版本)
- iOS Safari
- Chrome for Android

### 11.2 性能指标
- LCP (Largest Contentful Paint): < 2.5s
- FID (First Input Delay): < 100ms
- CLS (Cumulative Layout Shift): < 0.1
- Time to Interactive: < 3.8s

### 11.3 无障碍测试
- WAVE工具检测：0错误
- 键盘导航：所有功能可用
- 屏幕阅读器：内容可理解
- 颜色对比度：符合WCAG AA标准

---

本文档将随项目开发进展持续更新和完善。
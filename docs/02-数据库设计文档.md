# 多语言多域名宠物博客管理平台 - 数据库设计文档

## 1. 数据库概述

### 1.1 数据库基本信息
- **数据库类型**：MySQL 8.0
- **字符集**：utf8mb4
- **排序规则**：utf8mb4_unicode_ci
- **存储引擎**：InnoDB
- **时区**：UTC

### 1.2 命名规范
- **表名**：小写字母，单词间用下划线分隔
- **字段名**：小写字母，单词间用下划线分隔
- **索引名**：idx_表名_字段名
- **外键名**：fk_表名_关联表名

### 1.3 数据库连接信息
```yaml
development:
  host: localhost
  port: 3306
  database: pet_blog_dev
  username: dev_user
  password: dev_password

production:
  host: ************
  port: 3306
  database: bengtai
  username: bengtai
  password: weizhen258
  ssl: required
```

## 2. 数据表设计

### 2.1 用户和权限表

#### 2.1.1 admin_users（管理员用户表）
```sql
CREATE TABLE `admin_users` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `email` VARCHAR(100) NOT NULL COMMENT '邮箱',
  `password` VARCHAR(255) NOT NULL COMMENT '密码（bcrypt加密）',
  `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
  `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(45) DEFAULT NULL COMMENT '最后登录IP',
  `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';
```

#### 2.1.2 admin_logs（管理员操作日志表）
```sql
CREATE TABLE `admin_logs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` INT UNSIGNED NOT NULL COMMENT '管理员ID',
  `action` VARCHAR(100) NOT NULL COMMENT '操作动作',
  `module` VARCHAR(50) NOT NULL COMMENT '操作模块',
  `details` JSON DEFAULT NULL COMMENT '操作详情',
  `ip_address` VARCHAR(45) NOT NULL COMMENT 'IP地址',
  `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_admin_logs_admin` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';
```

### 2.2 内容管理表

#### 2.2.1 categories（分类表）
```sql
CREATE TABLE `categories` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_id` INT UNSIGNED DEFAULT NULL COMMENT '父分类ID',
  `slug` VARCHAR(100) NOT NULL COMMENT 'URL别名',
  `icon` VARCHAR(255) DEFAULT NULL COMMENT '图标',
  `sort_order` INT NOT NULL DEFAULT '0' COMMENT '排序',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_slug` (`slug`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_categories_parent` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';
```

#### 2.2.2 category_translations（分类翻译表）
```sql
CREATE TABLE `category_translations` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` INT UNSIGNED NOT NULL COMMENT '分类ID',
  `language` ENUM('zh', 'en', 'de', 'ru') NOT NULL COMMENT '语言',
  `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
  `description` TEXT DEFAULT NULL COMMENT '分类描述',
  `meta_title` VARCHAR(255) DEFAULT NULL COMMENT 'SEO标题',
  `meta_description` TEXT DEFAULT NULL COMMENT 'SEO描述',
  `meta_keywords` TEXT DEFAULT NULL COMMENT 'SEO关键词',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_category_language` (`category_id`, `language`),
  CONSTRAINT `fk_category_translations_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类翻译表';
```

#### 2.2.3 articles（文章表）
```sql
CREATE TABLE `articles` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` INT UNSIGNED NOT NULL COMMENT '分类ID',
  `author_id` INT UNSIGNED NOT NULL COMMENT '作者ID',
  `featured_image_id` INT UNSIGNED DEFAULT NULL COMMENT '特色图片ID',
  `status` ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft' COMMENT '状态',
  `view_count` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `is_featured` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否精选',
  `published_at` TIMESTAMP NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_status_published` (`status`, `published_at`),
  KEY `idx_featured_published` (`is_featured`, `published_at`),
  CONSTRAINT `fk_articles_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`),
  CONSTRAINT `fk_articles_author` FOREIGN KEY (`author_id`) REFERENCES `admin_users` (`id`),
  CONSTRAINT `fk_articles_image` FOREIGN KEY (`featured_image_id`) REFERENCES `images` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';
```

#### 2.2.4 article_translations（文章翻译表）
```sql
CREATE TABLE `article_translations` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` INT UNSIGNED NOT NULL COMMENT '文章ID',
  `language` ENUM('zh', 'en', 'de', 'ru') NOT NULL COMMENT '语言',
  `title` VARCHAR(255) NOT NULL COMMENT '标题',
  `slug` VARCHAR(255) NOT NULL COMMENT 'URL别名',
  `excerpt` TEXT DEFAULT NULL COMMENT '摘要',
  `content` MEDIUMTEXT NOT NULL COMMENT '内容',
  `meta_title` VARCHAR(255) DEFAULT NULL COMMENT 'SEO标题',
  `meta_description` TEXT DEFAULT NULL COMMENT 'SEO描述',
  `meta_keywords` TEXT DEFAULT NULL COMMENT 'SEO关键词',
  `is_translated` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已翻译',
  `is_reviewed` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已审核',
  `translated_at` TIMESTAMP NULL DEFAULT NULL COMMENT '翻译时间',
  `reviewed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '审核时间',
  `translator_id` INT UNSIGNED DEFAULT NULL COMMENT '翻译者ID',
  `reviewer_id` INT UNSIGNED DEFAULT NULL COMMENT '审核者ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_article_language` (`article_id`, `language`),
  UNIQUE KEY `idx_slug_language` (`slug`, `language`),
  FULLTEXT KEY `ft_title_content` (`title`, `content`),
  CONSTRAINT `fk_article_translations_article` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章翻译表';
```

#### 2.2.5 article_tags（文章标签关联表）
```sql
CREATE TABLE `article_tags` (
  `article_id` INT UNSIGNED NOT NULL COMMENT '文章ID',
  `tag_id` INT UNSIGNED NOT NULL COMMENT '标签ID',
  PRIMARY KEY (`article_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`),
  CONSTRAINT `fk_article_tags_article` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_tags_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章标签关联表';
```

### 2.3 媒体管理表

#### 2.3.1 images（图片表）
```sql
CREATE TABLE `images` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `filename` VARCHAR(255) NOT NULL COMMENT '文件名',
  `original_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `path` VARCHAR(500) NOT NULL COMMENT '文件路径',
  `url` VARCHAR(500) NOT NULL COMMENT '访问URL',
  `mime_type` VARCHAR(50) NOT NULL COMMENT 'MIME类型',
  `size` INT UNSIGNED NOT NULL COMMENT '文件大小（字节）',
  `width` INT UNSIGNED DEFAULT NULL COMMENT '宽度',
  `height` INT UNSIGNED DEFAULT NULL COMMENT '高度',
  `alt_text` VARCHAR(255) DEFAULT NULL COMMENT '替代文本',
  `uploaded_by` INT UNSIGNED NOT NULL COMMENT '上传者ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_images_uploader` FOREIGN KEY (`uploaded_by`) REFERENCES `admin_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片表';
```

#### 2.3.2 image_variants（图片变体表）
```sql
CREATE TABLE `image_variants` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `image_id` INT UNSIGNED NOT NULL COMMENT '原图ID',
  `variant_type` ENUM('thumbnail', 'small', 'medium', 'large', 'webp') NOT NULL COMMENT '变体类型',
  `path` VARCHAR(500) NOT NULL COMMENT '文件路径',
  `url` VARCHAR(500) NOT NULL COMMENT '访问URL',
  `width` INT UNSIGNED NOT NULL COMMENT '宽度',
  `height` INT UNSIGNED NOT NULL COMMENT '高度',
  `size` INT UNSIGNED NOT NULL COMMENT '文件大小',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_image_variant` (`image_id`, `variant_type`),
  CONSTRAINT `fk_image_variants_image` FOREIGN KEY (`image_id`) REFERENCES `images` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片变体表';
```

### 2.4 评论系统表

#### 2.4.1 comments（评论表）
```sql
CREATE TABLE `comments` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` INT UNSIGNED NOT NULL COMMENT '文章ID',
  `parent_id` BIGINT UNSIGNED DEFAULT NULL COMMENT '父评论ID',
  `author_name` VARCHAR(100) NOT NULL COMMENT '评论者名称',
  `author_email` VARCHAR(255) NOT NULL COMMENT '评论者邮箱',
  `author_url` VARCHAR(255) DEFAULT NULL COMMENT '评论者网址',
  `content` TEXT NOT NULL COMMENT '评论内容',
  `ip_address` VARCHAR(45) NOT NULL COMMENT 'IP地址',
  `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
  `status` ENUM('pending', 'approved', 'spam', 'trash') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `approved_by` INT UNSIGNED DEFAULT NULL COMMENT '审核者ID',
  `approved_at` TIMESTAMP NULL DEFAULT NULL COMMENT '审核时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article_status` (`article_id`, `status`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_author_email` (`author_email`),
  CONSTRAINT `fk_comments_article` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_parent` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_approver` FOREIGN KEY (`approved_by`) REFERENCES `admin_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';
```

### 2.5 系统配置表

#### 2.5.1 domains（域名配置表）
```sql
CREATE TABLE `domains` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `domain` VARCHAR(255) NOT NULL COMMENT '域名',
  `language` ENUM('en', 'de', 'ru') NOT NULL COMMENT '语言',
  `is_primary` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否主域名',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
  `ssl_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'SSL是否启用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_domain` (`domain`),
  KEY `idx_language` (`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名配置表';
```

#### 2.5.2 settings（系统设置表）
```sql
CREATE TABLE `settings` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `group` VARCHAR(50) NOT NULL COMMENT '设置组',
  `key` VARCHAR(100) NOT NULL COMMENT '设置键',
  `value` TEXT DEFAULT NULL COMMENT '设置值',
  `type` ENUM('string', 'number', 'boolean', 'json', 'text') NOT NULL DEFAULT 'string' COMMENT '值类型',
  `description` VARCHAR(500) DEFAULT NULL COMMENT '描述',
  `is_public` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_group_key` (`group`, `key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';
```

#### 2.5.3 ads_slots（广告位表）
```sql
CREATE TABLE `ads_slots` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL COMMENT '广告位名称',
  `code` VARCHAR(50) NOT NULL COMMENT '广告位代码',
  `position` VARCHAR(50) NOT NULL COMMENT '位置',
  `ad_code` TEXT DEFAULT NULL COMMENT '广告代码',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
  `languages` JSON NOT NULL COMMENT '适用语言',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告位表';
```

### 2.6 翻译管理表

#### 2.6.1 translation_jobs（翻译任务表）
```sql
CREATE TABLE `translation_jobs` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` INT UNSIGNED NOT NULL COMMENT '文章ID',
  `source_language` VARCHAR(10) NOT NULL DEFAULT 'zh' COMMENT '源语言',
  `target_languages` JSON NOT NULL COMMENT '目标语言列表',
  `status` ENUM('pending', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `progress` INT NOT NULL DEFAULT '0' COMMENT '进度百分比',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
  `started_at` TIMESTAMP NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '完成时间',
  `created_by` INT UNSIGNED NOT NULL COMMENT '创建者ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `fk_translation_jobs_article` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`),
  CONSTRAINT `fk_translation_jobs_creator` FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译任务表';
```

#### 2.6.2 translation_glossary（翻译术语表）
```sql
CREATE TABLE `translation_glossary` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `term` VARCHAR(255) NOT NULL COMMENT '术语',
  `language` ENUM('en', 'de', 'ru') NOT NULL COMMENT '语言',
  `translation` VARCHAR(255) NOT NULL COMMENT '翻译',
  `context` VARCHAR(500) DEFAULT NULL COMMENT '上下文说明',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_term_language` (`term`, `language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译术语表';
```

### 2.7 统计分析表

#### 2.7.1 article_views（文章浏览记录表）
```sql
CREATE TABLE `article_views` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` INT UNSIGNED NOT NULL COMMENT '文章ID',
  `ip_address` VARCHAR(45) NOT NULL COMMENT 'IP地址',
  `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
  `referer` VARCHAR(500) DEFAULT NULL COMMENT '来源页面',
  `language` VARCHAR(10) NOT NULL COMMENT '语言版本',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_ip_article_date` (`ip_address`, `article_id`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章浏览记录表';
```

## 3. 初始数据

### 3.1 默认管理员账户
```sql
INSERT INTO `admin_users` (`username`, `email`, `password`) VALUES
('admin', '<EMAIL>', '$2b$10$YourHashedPasswordHere');
```

### 3.2 默认分类数据
```sql
-- 插入主分类
INSERT INTO `categories` (`slug`, `sort_order`) VALUES
('dogs', 1),
('cats', 2);

-- 插入狗的子分类
INSERT INTO `categories` (`parent_id`, `slug`, `sort_order`) VALUES
(1, 'dog-breeds', 1),
(1, 'dog-training', 2),
(1, 'dog-health', 3),
(1, 'dog-nutrition', 4),
(1, 'dog-behavior', 5);

-- 插入猫的子分类
INSERT INTO `categories` (`parent_id`, `slug`, `sort_order`) VALUES
(2, 'cat-breeds', 1),
(2, 'cat-care', 2),
(2, 'cat-health', 3),
(2, 'cat-nutrition', 4),
(2, 'cat-behavior', 5);

-- 插入分类翻译
INSERT INTO `category_translations` (`category_id`, `language`, `name`, `meta_title`) VALUES
-- 主分类翻译
(1, 'zh', '狗狗', '狗狗知识分享'),
(1, 'en', 'Dogs', 'Dog Knowledge Sharing'),
(1, 'de', 'Hunde', 'Hundewissen teilen'),
(1, 'ru', 'Собаки', 'Знания о собаках'),
(2, 'zh', '猫咪', '猫咪知识分享'),
(2, 'en', 'Cats', 'Cat Knowledge Sharing'),
(2, 'de', 'Katzen', 'Katzenwissen teilen'),
(2, 'ru', 'Кошки', 'Знания о кошках');
```

### 3.3 默认系统设置
```sql
INSERT INTO `settings` (`group`, `key`, `value`, `type`, `description`) VALUES
-- 基础设置
('site', 'site_name', '宠物知识博客', 'string', '网站名称'),
('site', 'site_tagline', '专业的宠物知识分享平台', 'string', '网站标语'),
('site', 'admin_email', '<EMAIL>', 'string', '管理员邮箱'),
-- SEO设置
('seo', 'google_analytics', '', 'text', 'Google Analytics代码'),
('seo', 'google_adsense', '', 'text', 'Google AdSense代码'),
('seo', 'robots_txt', "User-agent: *\nAllow: /", 'text', 'robots.txt内容'),
-- 翻译设置
('translation', 'api_url', 'https://ai.wanderintree.top', 'string', '翻译API地址'),
('translation', 'api_key', 'sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d', 'string', '翻译API密钥'),
('translation', 'model', 'gemini-2.0-pro', 'string', '翻译模型'),
-- 评论设置
('comment', 'auto_approve', 'false', 'boolean', '自动审核评论'),
('comment', 'require_email', 'true', 'boolean', '评论需要邮箱'),
('comment', 'max_depth', '3', 'number', '最大嵌套层级');
```

### 3.4 默认域名配置
```sql
INSERT INTO `domains` (`domain`, `language`, `is_primary`) VALUES
('example.com', 'en', TRUE),
('example.de', 'de', TRUE),
('example.ru', 'ru', TRUE);
```

## 4. 索引优化

### 4.1 性能索引
```sql
-- 文章查询优化
CREATE INDEX idx_articles_status_featured_published 
ON articles(status, is_featured, published_at DESC);

-- 评论查询优化
CREATE INDEX idx_comments_article_status_created 
ON comments(article_id, status, created_at DESC);

-- 翻译查询优化
CREATE INDEX idx_article_translations_reviewed 
ON article_translations(is_reviewed, reviewed_at);

-- 浏览统计优化
CREATE INDEX idx_article_views_daily 
ON article_views(article_id, DATE(created_at));
```

### 4.2 全文搜索索引
```sql
-- 中文全文搜索支持
ALTER TABLE article_translations 
ADD FULLTEXT ft_title_content_zh (title, content) 
WITH PARSER ngram;

-- 英文全文搜索
ALTER TABLE article_translations 
ADD FULLTEXT ft_title_content_en (title, content);
```

## 5. 数据库维护

### 5.1 备份策略
```sql
-- 每日备份脚本
mysqldump -u bengtai -p bengtai \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  > backup_$(date +%Y%m%d).sql
```

### 5.2 性能优化建议
1. **定期优化表**：`OPTIMIZE TABLE table_name`
2. **查询缓存**：启用MySQL查询缓存
3. **连接池**：应用层使用连接池
4. **读写分离**：未来考虑主从复制

### 5.3 监控指标
- 慢查询日志：`slow_query_log = 1`
- 查询时间阈值：`long_query_time = 2`
- 连接数监控：`max_connections = 200`
- 缓冲池大小：`innodb_buffer_pool_size = 1G`

## 6. 数据字典

### 6.1 枚举类型说明

#### 文章状态（article.status）
- `draft`：草稿
- `published`：已发布
- `archived`：已归档

#### 评论状态（comment.status）
- `pending`：待审核
- `approved`：已通过
- `spam`：垃圾评论
- `trash`：回收站

#### 语言代码（language）
- `zh`：中文
- `en`：英语
- `de`：德语
- `ru`：俄语

### 6.2 关键业务规则
1. 删除分类时，子分类级联删除
2. 删除文章时，翻译和评论级联删除
3. 每篇文章必须有中文原文
4. 同一语言下文章slug必须唯一

---

本文档将随项目开发进展持续更新和完善。
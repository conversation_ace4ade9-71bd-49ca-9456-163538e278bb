# 多语言多域名宠物博客管理平台 - API接口文档

## 1. API概述

### 1.1 基础信息
- **基础URL**：`https://api.example.com/api/v1`
- **协议**：HTTPS
- **数据格式**：JSON
- **字符编码**：UTF-8
- **API版本**：v1

### 1.2 认证方式
使用JWT (JSON Web Token) Bearer认证：
```
Authorization: Bearer {your_jwt_token}
```

### 1.3 通用请求头
```http
Content-Type: application/json
Accept: application/json
Accept-Language: zh-CN,en,de,ru
X-Domain: example.com
```

### 1.4 通用响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 实际数据
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid-v4"
  }
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "Validation Error",
  "errors": [
    {
      "field": "title",
      "message": "Title is required",
      "code": "REQUIRED_FIELD"
    }
  ],
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid-v4"
  }
}
```

### 1.5 错误码定义
| 错误码 | 描述 | HTTP状态码 |
|--------|------|------------|
| 200 | 成功 | 200 |
| 201 | 创建成功 | 201 |
| 204 | 无内容 | 204 |
| 400 | 请求参数错误 | 400 |
| 401 | 未认证 | 401 |
| 403 | 无权限 | 403 |
| 404 | 资源不存在 | 404 |
| 409 | 资源冲突 | 409 |
| 422 | 无法处理的实体 | 422 |
| 429 | 请求过多 | 429 |
| 500 | 服务器内部错误 | 500 |
| 503 | 服务不可用 | 503 |

## 2. 认证接口

### 2.1 登录
**POST** `/api/v1/auth/login`

#### 请求参数
```json
{
  "username": "admin",
  "password": "password123"
}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400,
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar.jpg"
    }
  }
}
```

### 2.2 刷新Token
**POST** `/api/v1/auth/refresh`

#### 请求头
```http
Authorization: Bearer {your_refresh_token}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "Token refreshed",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400
  }
}
```

### 2.3 登出
**POST** `/api/v1/auth/logout`

#### 请求头
```http
Authorization: Bearer {your_jwt_token}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "Logout successful"
}
```

## 3. 文章管理接口

### 3.1 获取文章列表
**GET** `/api/v1/articles`

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认20 |
| status | string | 否 | 状态：draft, published, archived |
| category_id | integer | 否 | 分类ID |
| language | string | 否 | 语言：zh, en, de, ru |
| search | string | 否 | 搜索关键词 |
| sort | string | 否 | 排序字段：created_at, published_at, view_count |
| order | string | 否 | 排序方向：asc, desc |

#### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": 1,
        "category": {
          "id": 1,
          "name": "狗狗品种",
          "slug": "dog-breeds"
        },
        "translations": {
          "zh": {
            "title": "金毛寻回犬饲养指南",
            "slug": "golden-retriever-guide",
            "excerpt": "金毛寻回犬是最受欢迎的家庭犬之一...",
            "is_translated": true,
            "is_reviewed": true
          },
          "en": {
            "title": "Golden Retriever Care Guide",
            "slug": "golden-retriever-care-guide",
            "excerpt": "Golden Retrievers are one of the most popular family dogs...",
            "is_translated": true,
            "is_reviewed": true
          }
        },
        "featured_image": {
          "id": 1,
          "url": "https://example.com/images/golden-retriever.jpg",
          "alt_text": "Golden Retriever"
        },
        "status": "published",
        "view_count": 1234,
        "is_featured": true,
        "published_at": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 20,
      "pages": 5
    }
  }
}
```

### 3.2 获取文章详情
**GET** `/api/v1/articles/{id}`

#### 路径参数
- `id`: 文章ID

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| language | string | 否 | 语言版本，默认返回所有语言 |

#### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": 1,
    "category": {
      "id": 1,
      "name": "狗狗品种",
      "slug": "dog-breeds",
      "parent": {
        "id": 1,
        "name": "狗狗",
        "slug": "dogs"
      }
    },
    "author": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>"
    },
    "translations": {
      "zh": {
        "title": "金毛寻回犬饲养指南",
        "slug": "golden-retriever-guide",
        "excerpt": "金毛寻回犬是最受欢迎的家庭犬之一...",
        "content": "<h2>品种介绍</h2><p>金毛寻回犬...</p>",
        "meta_title": "金毛寻回犬饲养指南 - 宠物博客",
        "meta_description": "详细介绍金毛寻回犬的饲养方法...",
        "meta_keywords": "金毛,寻回犬,饲养",
        "is_translated": true,
        "is_reviewed": true
      },
      "en": {
        "title": "Golden Retriever Care Guide",
        "slug": "golden-retriever-care-guide",
        "excerpt": "Golden Retrievers are one of the most popular family dogs...",
        "content": "<h2>Breed Introduction</h2><p>Golden Retrievers...</p>",
        "meta_title": "Golden Retriever Care Guide - Pet Blog",
        "meta_description": "Detailed guide on caring for Golden Retrievers...",
        "meta_keywords": "golden retriever,care,guide",
        "is_translated": true,
        "is_reviewed": true
      }
    },
    "featured_image": {
      "id": 1,
      "url": "https://example.com/images/golden-retriever.jpg",
      "alt_text": "Golden Retriever",
      "variants": {
        "thumbnail": "https://example.com/images/golden-retriever-thumb.jpg",
        "medium": "https://example.com/images/golden-retriever-medium.jpg",
        "large": "https://example.com/images/golden-retriever-large.jpg",
        "webp": "https://example.com/images/golden-retriever.webp"
      }
    },
    "tags": [
      {
        "id": 1,
        "name": "大型犬",
        "slug": "large-dogs"
      }
    ],
    "status": "published",
    "view_count": 1234,
    "is_featured": true,
    "published_at": "2024-01-01T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3.3 创建文章
**POST** `/api/v1/articles`

#### 请求参数
```json
{
  "category_id": 1,
  "featured_image_id": 1,
  "status": "draft",
  "is_featured": false,
  "translations": {
    "zh": {
      "title": "金毛寻回犬饲养指南",
      "slug": "golden-retriever-guide",
      "excerpt": "金毛寻回犬是最受欢迎的家庭犬之一...",
      "content": "<h2>品种介绍</h2><p>金毛寻回犬...</p>",
      "meta_title": "金毛寻回犬饲养指南 - 宠物博客",
      "meta_description": "详细介绍金毛寻回犬的饲养方法...",
      "meta_keywords": "金毛,寻回犬,饲养"
    }
  },
  "tags": [1, 2, 3]
}
```

#### 响应示例
```json
{
  "code": 201,
  "message": "Article created successfully",
  "data": {
    "id": 1,
    // 完整的文章数据
  }
}
```

### 3.4 更新文章
**PUT** `/api/v1/articles/{id}`

#### 路径参数
- `id`: 文章ID

#### 请求参数
与创建文章相同，但所有字段都是可选的

### 3.5 删除文章
**DELETE** `/api/v1/articles/{id}`

#### 路径参数
- `id`: 文章ID

#### 响应示例
```json
{
  "code": 200,
  "message": "Article deleted successfully"
}
```

### 3.6 批量操作文章
**POST** `/api/v1/articles/batch`

#### 请求参数
```json
{
  "action": "publish", // publish, unpublish, delete, archive
  "ids": [1, 2, 3, 4, 5]
}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "Batch operation completed",
  "data": {
    "success": 5,
    "failed": 0,
    "results": [
      {"id": 1, "status": "success"},
      {"id": 2, "status": "success"},
      {"id": 3, "status": "success"},
      {"id": 4, "status": "success"},
      {"id": 5, "status": "success"}
    ]
  }
}
```

## 4. 翻译管理接口

### 4.1 创建翻译任务
**POST** `/api/v1/articles/{id}/translate`

#### 路径参数
- `id`: 文章ID

#### 请求参数
```json
{
  "source_language": "zh",
  "target_languages": ["en", "de", "ru"],
  "translate_meta": true
}
```

#### 响应示例
```json
{
  "code": 202,
  "message": "Translation job created",
  "data": {
    "job_id": 123,
    "status": "pending",
    "progress": 0,
    "estimated_time": 60
  }
}
```

### 4.2 获取翻译任务状态
**GET** `/api/v1/translations/jobs/{job_id}`

#### 路径参数
- `job_id`: 翻译任务ID

#### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "job_id": 123,
    "article_id": 1,
    "status": "processing",
    "progress": 50,
    "completed_languages": ["en"],
    "pending_languages": ["de", "ru"],
    "error_message": null,
    "started_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4.3 获取文章翻译版本
**GET** `/api/v1/articles/{id}/translations`

#### 路径参数
- `id`: 文章ID

#### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "zh": {
      "language": "zh",
      "title": "金毛寻回犬饲养指南",
      "is_translated": false,
      "is_reviewed": true,
      "translated_at": null,
      "reviewed_at": "2024-01-01T00:00:00Z"
    },
    "en": {
      "language": "en",
      "title": "Golden Retriever Care Guide",
      "is_translated": true,
      "is_reviewed": true,
      "translated_at": "2024-01-01T00:00:00Z",
      "reviewed_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

### 4.4 更新翻译内容
**PUT** `/api/v1/articles/{id}/translations/{language}`

#### 路径参数
- `id`: 文章ID
- `language`: 语言代码（en, de, ru）

#### 请求参数
```json
{
  "title": "Golden Retriever Care Guide",
  "slug": "golden-retriever-care-guide",
  "excerpt": "Updated excerpt...",
  "content": "Updated content...",
  "meta_title": "Updated meta title",
  "meta_description": "Updated meta description",
  "meta_keywords": "updated,keywords",
  "is_reviewed": true
}
```

## 5. 分类管理接口

### 5.1 获取分类树
**GET** `/api/v1/categories/tree`

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| language | string | 否 | 语言版本 |

#### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": 1,
      "slug": "dogs",
      "name": "狗狗",
      "icon": "🐕",
      "children": [
        {
          "id": 3,
          "slug": "dog-breeds",
          "name": "狗狗品种",
          "icon": null,
          "children": []
        }
      ]
    },
    {
      "id": 2,
      "slug": "cats",
      "name": "猫咪",
      "icon": "🐈",
      "children": [
        {
          "id": 8,
          "slug": "cat-breeds",
          "name": "猫咪品种",
          "icon": null,
          "children": []
        }
      ]
    }
  ]
}
```

### 5.2 创建分类
**POST** `/api/v1/categories`

#### 请求参数
```json
{
  "parent_id": 1,
  "slug": "dog-training",
  "icon": "🎯",
  "sort_order": 2,
  "translations": {
    "zh": {
      "name": "狗狗训练",
      "description": "狗狗训练相关文章",
      "meta_title": "狗狗训练技巧"
    },
    "en": {
      "name": "Dog Training",
      "description": "Dog training articles",
      "meta_title": "Dog Training Tips"
    }
  }
}
```

## 6. 评论管理接口

### 6.1 获取评论列表
**GET** `/api/v1/comments`

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| article_id | integer | 否 | 文章ID |
| status | string | 否 | 状态：pending, approved, spam, trash |
| page | integer | 否 | 页码 |
| limit | integer | 否 | 每页数量 |

### 6.2 提交评论
**POST** `/api/v1/comments`

#### 请求参数
```json
{
  "article_id": 1,
  "parent_id": null,
  "author_name": "张三",
  "author_email": "<EMAIL>",
  "author_url": "https://example.com",
  "content": "这篇文章写得很好，对我帮助很大！"
}
```

### 6.3 审核评论
**PUT** `/api/v1/comments/{id}/approve`

#### 路径参数
- `id`: 评论ID

### 6.4 批量审核评论
**POST** `/api/v1/comments/batch-approve`

#### 请求参数
```json
{
  "ids": [1, 2, 3, 4, 5],
  "status": "approved" // approved, spam, trash
}
```

## 7. 媒体管理接口

### 7.1 上传图片
**POST** `/api/v1/media/upload`

#### 请求格式
- Content-Type: multipart/form-data

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| file | file | 是 | 图片文件 |
| alt_text | string | 否 | 替代文本 |

#### 响应示例
```json
{
  "code": 201,
  "message": "Image uploaded successfully",
  "data": {
    "id": 1,
    "filename": "golden-retriever-20240101.jpg",
    "url": "https://example.com/uploads/2024/01/golden-retriever-20240101.jpg",
    "mime_type": "image/jpeg",
    "size": 204800,
    "width": 1920,
    "height": 1080,
    "variants": {
      "thumbnail": "https://example.com/uploads/2024/01/golden-retriever-20240101-thumb.jpg",
      "medium": "https://example.com/uploads/2024/01/golden-retriever-20240101-medium.jpg",
      "large": "https://example.com/uploads/2024/01/golden-retriever-20240101-large.jpg",
      "webp": "https://example.com/uploads/2024/01/golden-retriever-20240101.webp"
    }
  }
}
```

### 7.2 获取图片列表
**GET** `/api/v1/media/images`

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | integer | 否 | 页码 |
| limit | integer | 否 | 每页数量 |
| sort | string | 否 | 排序：created_at, size, name |

## 8. 搜索接口

### 8.1 全站搜索
**GET** `/api/v1/search`

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| q | string | 是 | 搜索关键词 |
| type | string | 否 | 类型：article, category |
| language | string | 否 | 语言版本 |
| page | integer | 否 | 页码 |
| limit | integer | 否 | 每页数量 |

#### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "type": "article",
        "id": 1,
        "title": "金毛寻回犬饲养指南",
        "excerpt": "金毛寻回犬是最受欢迎的家庭犬之一...",
        "url": "/dogs/golden-retriever-guide",
        "highlight": {
          "title": "<mark>金毛</mark>寻回犬饲养指南",
          "content": "...<mark>金毛</mark>寻回犬是最受欢迎的..."
        }
      }
    ],
    "pagination": {
      "total": 25,
      "page": 1,
      "limit": 10,
      "pages": 3
    }
  }
}
```

## 9. 系统管理接口

### 9.1 获取系统设置
**GET** `/api/v1/system/settings`

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| group | string | 否 | 设置组：site, seo, comment, translation |

### 9.2 更新系统设置
**PUT** `/api/v1/system/settings`

#### 请求参数
```json
{
  "site.site_name": "宠物知识博客",
  "site.site_tagline": "专业的宠物知识分享平台",
  "seo.google_analytics": "GA-XXXXXXXX",
  "comment.auto_approve": false
}
```

### 9.3 获取域名配置
**GET** `/api/v1/system/domains`

### 9.4 更新域名配置
**PUT** `/api/v1/system/domains/{id}`

#### 请求参数
```json
{
  "domain": "example.com",
  "language": "en",
  "is_primary": true,
  "is_active": true
}
```

## 10. 统计分析接口

### 10.1 获取仪表板数据
**GET** `/api/v1/stats/dashboard`

#### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "overview": {
      "total_articles": 150,
      "published_articles": 120,
      "total_comments": 580,
      "pending_comments": 12,
      "total_views": 45678,
      "today_views": 234
    },
    "recent_articles": [
      // 最近5篇文章
    ],
    "popular_articles": [
      // 热门5篇文章
    ],
    "recent_comments": [
      // 最近5条评论
    ]
  }
}
```

### 10.2 获取文章统计
**GET** `/api/v1/stats/articles`

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| period | string | 否 | 时间段：today, week, month, year |
| start_date | date | 否 | 开始日期 |
| end_date | date | 否 | 结束日期 |

## 11. WebSocket接口

### 11.1 实时通知
**WebSocket** `/ws/notifications`

#### 连接认证
```json
{
  "type": "auth",
  "token": "your_jwt_token"
}
```

#### 消息格式
```json
{
  "type": "notification",
  "data": {
    "id": "uuid",
    "type": "new_comment",
    "message": "您有一条新评论",
    "data": {
      "article_id": 1,
      "comment_id": 123
    },
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

## 12. 限流策略

### 12.1 限流规则
- 未认证用户：60请求/分钟
- 认证用户：600请求/分钟
- 特殊接口限制：
  - 登录：5次/分钟
  - 上传：10次/分钟
  - 翻译：20次/分钟

### 12.2 限流响应头
```http
X-RateLimit-Limit: 600
X-RateLimit-Remaining: 599
X-RateLimit-Reset: 1704067200
```

## 13. API版本控制

### 13.1 版本策略
- URL版本：`/api/v1/`, `/api/v2/`
- 向后兼容：至少支持2个大版本
- 废弃通知：提前3个月通知

### 13.2 版本升级响应头
```http
X-API-Deprecation-Date: 2024-12-31
X-API-Deprecation-Info: https://docs.example.com/api/v2/migration
```

## 14. 安全建议

### 14.1 HTTPS强制
所有API请求必须使用HTTPS协议

### 14.2 CORS配置
```http
Access-Control-Allow-Origin: https://example.com
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
Access-Control-Max-Age: 86400
```

### 14.3 安全响应头
```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
```

---

本文档将随项目开发进展持续更新和完善。
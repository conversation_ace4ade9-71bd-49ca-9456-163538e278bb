# 多语言多域名宠物博客管理平台 - 项目架构设计文档

## 1. 项目概述

### 1.1 项目背景
本项目是一个专注于猫狗宠物知识分享的多语言博客平台，通过优质内容和SEO优化在Google搜索中获得优秀排名。平台支持多语言（英语、德语、俄语）和多域名架构，具备完整的内容管理和翻译工作流。

### 1.2 核心目标
- **SEO优先**：所有技术决策以Google SEO最佳实践为准则
- **高性能**：支持1万+并发用户访问
- **可扩展**：轻松添加新语言和新功能
- **易维护**：清晰的代码结构和完善的文档

### 1.3 技术栈选型

#### 前端技术栈
- **框架**：Astro 3.0 - 静态站点生成器
- **UI框架**：Tailwind CSS 3.0
- **JavaScript**：原生ES6+
- **构建工具**：Vite

#### 后端技术栈
- **运行时**：Node.js 18 LTS
- **框架**：Express 4.18
- **ORM**：Sequelize 6.0
- **认证**：JWT (jsonwebtoken)
- **日志**：Winston
- **进程管理**：PM2

#### 数据库
- **主数据库**：MySQL 8.0
- **缓存**：Redis 6.0（可选）
- **会话存储**：Redis/MySQL

#### 部署环境
- **服务器**：Linux VPS
- **Web服务器**：Nginx
- **SSL**：Let's Encrypt
- **管理面板**：宝塔面板

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        用户访问层                              │
├─────────────────┬─────────────────┬─────────────────────────┤
│   example.com   │   example.de    │     example.ru          │
│    (英语站)      │    (德语站)      │      (俄语站)           │
└────────┬────────┴────────┬────────┴──────────┬─────────────┘
         │                 │                    │
         └─────────────────┴────────────────────┘
                           │
                    ┌──────▼──────┐
                    │   Nginx     │
                    │ (反向代理)   │
                    └──────┬──────┘
                           │
         ┌─────────────────┴─────────────────┐
         │                                   │
    ┌────▼─────┐                      ┌─────▼─────┐
    │  Astro   │                      │  Express  │
    │ (前端SSG) │                      │ (后端API) │
    └────┬─────┘                      └─────┬─────┘
         │                                   │
         └─────────────┬─────────────────────┘
                       │
                ┌──────▼──────┐
                │   MySQL 8   │
                │  (主数据库)  │
                └─────────────┘
```

### 2.2 多语言架构设计

#### 2.2.1 域名语言映射
```javascript
const domainLanguageMap = {
  'example.com': 'en',
  'www.example.com': 'en',
  'example.de': 'de',
  'www.example.de': 'de',
  'example.ru': 'ru',
  'www.example.ru': 'ru'
};
```

#### 2.2.2 模板结构
```
/templates/
├── /shared/              # 共享组件和资源
│   ├── /components/      # 通用组件
│   ├── /layouts/         # 布局模板
│   └── /assets/          # 共享资源
├── /en/                  # 英语模板
│   ├── /pages/          # 页面模板
│   ├── /components/     # 语言特定组件
│   └── /locales/        # 语言包
├── /de/                  # 德语模板
└── /ru/                  # 俄语模板
```

### 2.3 数据流架构

#### 2.3.1 内容发布流程
```
管理员 → 后台编辑器 → 中文原文 → 翻译API → 多语言草稿 → 人工审核 → 发布
```

#### 2.3.2 用户访问流程
```
用户 → CDN → Nginx → 静态页面/API请求 → 数据库 → 响应
```

### 2.4 API架构设计

#### 2.4.1 RESTful API规范
- 基础路径：`/api/v1`
- 认证方式：Bearer Token (JWT)
- 响应格式：JSON
- 版本控制：URL版本号

#### 2.4.2 核心API模块
1. **认证模块** (`/api/v1/auth`)
2. **文章模块** (`/api/v1/articles`)
3. **分类模块** (`/api/v1/categories`)
4. **评论模块** (`/api/v1/comments`)
5. **媒体模块** (`/api/v1/media`)
6. **翻译模块** (`/api/v1/translations`)
7. **系统模块** (`/api/v1/system`)

## 3. 前端架构设计

### 3.1 Astro项目结构
```
/frontend/
├── /src/
│   ├── /components/      # 组件库
│   │   ├── /common/     # 通用组件
│   │   ├── /article/    # 文章相关组件
│   │   ├── /seo/        # SEO组件
│   │   └── /ui/         # UI组件
│   ├── /layouts/        # 布局模板
│   ├── /pages/          # 页面路由
│   ├── /styles/         # 样式文件
│   └── /utils/          # 工具函数
├── /public/             # 静态资源
└── /templates/          # 多语言模板
```

### 3.2 页面路由设计
```
/                    # 首页
/dogs/               # 狗分类页
/dogs/[slug]         # 狗文章详情
/cats/               # 猫分类页
/cats/[slug]         # 猫文章详情
/search              # 搜索页
/about               # 关于我们
/privacy             # 隐私政策
/sitemap.xml         # 站点地图
```

### 3.3 组件设计原则
1. **原子化设计**：从基础组件构建复杂组件
2. **响应式优先**：移动端优先的设计策略
3. **性能优化**：懒加载、代码分割
4. **SEO友好**：语义化HTML、结构化数据

## 4. 后端架构设计

### 4.1 Express项目结构
```
/backend/
├── /src/
│   ├── /config/         # 配置文件
│   ├── /controllers/    # 控制器
│   ├── /middleware/     # 中间件
│   ├── /models/         # 数据模型
│   ├── /routes/         # 路由定义
│   ├── /services/       # 业务服务
│   ├── /utils/          # 工具函数
│   └── /validators/     # 数据验证
├── /uploads/            # 上传文件
├── /logs/               # 日志文件
└── /tests/              # 测试文件
```

### 4.2 中间件架构
```javascript
// 中间件执行顺序
app.use(cors());                    // CORS处理
app.use(helmet());                  // 安全头部
app.use(compression());             // Gzip压缩
app.use(morgan('combined'));        // 访问日志
app.use(express.json());            // JSON解析
app.use(rateLimiter);              // 速率限制
app.use(authMiddleware);           // 认证中间件
app.use(domainDetector);           // 域名检测
app.use(languageDetector);         // 语言检测
app.use(errorHandler);             // 错误处理
```

### 4.3 服务层设计
```
/services/
├── authService.js          # 认证服务
├── articleService.js       # 文章服务
├── translationService.js   # 翻译服务
├── imageService.js         # 图片处理服务
├── cacheService.js         # 缓存服务
├── emailService.js         # 邮件服务
└── searchService.js        # 搜索服务
```

## 5. 数据库架构设计

### 5.1 核心数据表关系
```
articles (1) ─── (n) article_translations
    │
    ├──── (1) categories
    │          │
    │          └─── (n) category_translations
    │
    ├──── (n) comments
    │
    └──── (n) images
```

### 5.2 索引策略
1. **主键索引**：所有表的id字段
2. **外键索引**：所有关联字段
3. **复合索引**：(article_id, language), (slug, language)
4. **全文索引**：文章内容、标题字段

## 6. 安全架构设计

### 6.1 安全策略
1. **认证授权**：JWT Token + 角色权限
2. **数据验证**：输入验证 + 参数化查询
3. **XSS防护**：内容过滤 + CSP头部
4. **CSRF防护**：Token验证
5. **DDoS防护**：速率限制 + CDN防护

### 6.2 数据保护
1. **敏感数据加密**：密码bcrypt加密
2. **HTTPS强制**：全站SSL加密
3. **备份策略**：每日自动备份
4. **访问控制**：IP白名单 + 防火墙

## 7. 性能优化架构

### 7.1 前端性能优化
1. **静态资源优化**
   - 图片压缩和WebP格式
   - CSS/JS压缩和合并
   - 静态资源CDN加速

2. **渲染优化**
   - SSG静态生成
   - Critical CSS内联
   - 懒加载实现

### 7.2 后端性能优化
1. **数据库优化**
   - 查询优化和索引
   - 连接池管理
   - 读写分离（未来）

2. **缓存策略**
   - Redis缓存热点数据
   - HTTP缓存头设置
   - CDN边缘缓存

## 8. 监控和日志架构

### 8.1 监控体系
1. **应用监控**：响应时间、错误率、吞吐量
2. **服务器监控**：CPU、内存、磁盘、网络
3. **业务监控**：PV/UV、转化率、用户行为

### 8.2 日志体系
1. **应用日志**：Winston分级日志
2. **访问日志**：Nginx访问日志
3. **错误日志**：错误追踪和告警
4. **审计日志**：操作记录和安全审计

## 9. 扩展性设计

### 9.1 水平扩展
1. **无状态设计**：应用服务器无状态
2. **负载均衡**：Nginx负载均衡
3. **数据库扩展**：主从复制、分库分表

### 9.2 功能扩展
1. **插件化架构**：功能模块化设计
2. **微服务预留**：服务拆分接口预留
3. **API版本控制**：向后兼容设计

## 10. 开发和部署流程

### 10.1 开发流程
1. **版本控制**：Git分支管理
2. **代码审查**：PR审查机制
3. **自动化测试**：单元测试 + 集成测试
4. **持续集成**：CI/CD流程

### 10.2 部署流程
1. **环境隔离**：开发、测试、生产环境
2. **自动化部署**：脚本化部署流程
3. **回滚机制**：快速回滚能力
4. **灰度发布**：分批发布策略

---

本文档将随项目开发进展持续更新和完善。
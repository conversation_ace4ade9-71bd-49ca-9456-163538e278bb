# 多语言多域名宠物博客管理平台 - AI开发提示词

## 项目概述与背景

### 项目定位
开发一个专业的**多语言多域名宠物博客管理平台**，专注于猫狗宠物知识分享，通过优质内容和SEO优化在Google搜索中获得优秀排名。

### 核心目标
- **SEO导向**：所有技术决策均以Google SEO最佳实践为准则
- **内容质量**：提供高质量的宠物知识内容，服务全球宠物爱好者
- **多语言覆盖**：初期覆盖美国（英语）、德国（德语）、俄罗斯（俄语）市场
- **扩展性**：具备快速增加新语言和新市场的能力

### 业务模式
- **内容驱动**：纯博客模式，不提供商品销售或其他服务
- **广告变现**：通过Google AdSense等广告平台实现收益
- **流量目标**：预计前期1万访问量，具备扩展到更大规模的能力

## 技术架构要求

### 整体架构原则
- **性能优先**：Core Web Vitals指标必须达到Good等级
- **SEO友好**：所有技术选型均考虑SEO影响
- **可维护性**：代码结构清晰，便于长期维护
- **扩展性**：支持快速添加新语言和新功能

### 技术栈规格

#### 前端要求
- **框架**：Astro（已确定）
  - 理由：静态生成、SEO友好、性能优秀
  - 要求：充分利用Astro的SSG能力
  - 集成：支持多语言模板系统

#### 后端选型标准
请根据以下标准选择最适合的后端技术：
- **性能要求**：支持并发访问1万+用户
- **开发效率**：快速开发和部署
- **生态成熟度**：有丰富的插件和扩展
- **维护成本**：长期维护友好
- **推荐考虑**：Node.js/Express、Python/FastAPI、Go等

#### 数据库设计
- **主数据库**：MySQL 8.0+
- **性能要求**：优化查询性能，支持全文搜索
- **数据完整性**：严格的数据约束和事务管理
- **备份策略**：虽然用户说不需要自动备份，但需要支持手动备份

#### 部署环境
- **服务器**：Linux VPS + 宝塔面板
- **域名管理**：多顶级域名绑定不同语言
- **HTTPS**：强制HTTPS，SSL证书自动管理
- **CDN**：图片和静态资源加速

## 核心功能模块详细规格

### 1. 多语言架构系统

#### 语言模板管理
- **实现方式**：每种语言独立的Astro项目模板
  - 英语模板：`/templates/en/`
  - 德语模板：`/templates/de/`
  - 俄语模板：`/templates/ru/`
- **模板结构**：完全独立的HTML结构、CSS样式、JavaScript逻辑
- **URL结构**：本地化语言的URL Slug
  - 英语：`/dogs/training-tips`
  - 德语：`/hunde/training-tipps`
  - 俄语：`/sobaki/sovety-po-dressirovke`

#### 域名语言绑定
- **绑定机制**：后台配置域名对应的语言模板
- **实现要求**：
  - 动态路由识别域名
  - 自动加载对应语言模板
  - 支持本地开发环境测试（通过hosts文件或其他方案）

#### 新语言扩展流程
- **标准化流程**：
  1. 复制现有语言模板（如英语模板）
  2. 翻译所有静态文本内容
  3. 调整本地化元素（日期格式、数字格式等）
  4. 配置语言特定的SEO设置
  5. 后端添加语言支持和路由配置
- **工具支持**：提供半自动化的模板复制和翻译工具

### 2. 内容管理系统（CMS）

#### 文章管理功能
- **创建流程**：
  1. 富文本编辑器输入中文原文
  2. 本地图片直接粘贴上传
  3. 设置文章分类和SEO信息
  4. 保存为草稿状态

#### 翻译工作流
- **翻译流程**：
  1. 中文原文 → 点击"翻译"按钮
  2. 调用OpenAI API批量翻译成目标语言
  3. 翻译结果保存到各语言的草稿箱
  4. 人工校对和编辑翻译内容
  5. 校对完成后点击"发布"到前端页面

- **翻译API集成**：
  - API地址：https://ai.wanderintree.top
  - 密钥：sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
  - 模型：gemini-2.5-pro
  - 接口标准：OpenAI格式

#### 图片管理系统
- **存储方式**：本地服务器存储
- **处理流程**：
  1. 图片上传后自动压缩优化
  2. 生成多种尺寸版本（响应式需要）
  3. 自动生成WebP格式（浏览器支持时）
  4. 统一的图片URL管理

#### 富文本编辑器要求
- **功能需求**：
  - 支持图片直接粘贴
  - 所见即所得编辑
  - HTML源码编辑模式
  - 图片拖拽上传
  - 内容样式预览

### 3. 分类系统设计

#### 分类结构（两级分类）
- **狗类分类**：
  - 一级：狗狗 (Dogs)
    - 二级：品种介绍、训练技巧、健康护理、营养指南、行为解读
- **猫类分类**：
  - 一级：猫咪 (Cats)  
    - 二级：品种介绍、护理技巧、健康管理、营养指南、行为解读

#### 多语言分类名称
- **实现要求**：分类名称支持多语言
- **管理方式**：后台统一管理，每个分类配置多语言名称
- **URL生成**：根据语言生成对应的分类URL

### 4. 前端页面系统

#### 必需页面清单
1. **首页**：精选文章展示、分类导航、搜索功能
2. **分类页**：分类文章列表、分页、筛选
3. **文章详情页**：文章内容、评论系统、相关文章推荐
4. **搜索结果页**：搜索功能、结果分页
5. **关于我们页**：网站介绍（符合SEO需要）
6. **隐私政策页**：GDPR合规（特别是德国市场）
7. **404错误页**：用户友好的错误提示

#### 前端与后端交互接口
- **文章列表API**：获取分类文章、分页数据
- **文章详情API**：获取单篇文章完整内容
- **搜索API**：全文搜索功能
- **评论API**：提交评论、获取评论列表
- **分类API**：获取分类树结构
- **相关文章API**：基于标签或分类的相关推荐

### 5. 评论系统

#### 评论功能规格
- **用户信息**：仅需用户名和邮箱（无需注册）
- **嵌套回复**：支持多层嵌套回复（建议最多3层）
- **内容要求**：纯文本评论，支持基本HTML标签
- **审核机制**：所有评论必须后台审核通过才能显示

#### 反垃圾策略
- **基础验证**：邮箱格式验证、内容长度限制
- **频率限制**：同一IP限制评论频率
- **关键词过滤**：自动识别垃圾内容
- **人工审核**：管理员手动审核机制

### 6. SEO优化系统

#### 页面SEO要求
- **Title标签**：每页唯一、包含关键词、符合长度要求
- **Meta描述**：吸引点击的描述文字、长度控制在160字符内
- **H1-H6标签**：合理的标题层次结构
- **URL结构**：简洁、语义化、包含关键词
- **内链策略**：相关文章推荐、面包屑导航

#### 结构化数据实现
- **文章类型**：Article schema
- **面包屑**：BreadcrumbList schema  
- **网站信息**：Organization schema
- **FAQ类型**：FAQPage schema（适用时）

#### 技术SEO要求
- **网站地图**：自动生成XML sitemap
- **Robots.txt**：合理的爬虫指令
- **Core Web Vitals**：
  - LCP < 2.5秒
  - FID < 100毫秒  
  - CLS < 0.1
- **移动友好**：响应式设计、移动优先

### 7. 广告管理系统

#### 广告位设置
- **推荐位置**：
  - 文章开头（不影响阅读体验）
  - 文章中间（自然插入）
  - 侧边栏（桌面端）
  - 文章结尾
- **避免位置**：首屏主要内容区域

#### 广告控制功能  
- **全局开关**：每个语言网站独立的广告开关
- **位置控制**：每个广告位可独立开关
- **代码管理**：
  - Google AdSense代码
  - Google Analytics代码
  - 其他统计代码
- **无广告状态**：关闭时不显示任何占位符或空白区域

### 8. 后台管理系统

#### 管理员功能
- **单管理员模式**：只需要一个管理员账户
- **权限管理**：管理员具有所有操作权限
- **安全要求**：
  - 强密码策略
  - 登录日志记录
  - Session管理
  - CSRF保护

#### 管理界面要求
- **响应式设计**：支持手机和平板管理
- **用户体验**：直观的操作界面、清晰的信息反馈
- **批量操作**：支持批量发布、删除、修改文章
- **数据统计**：文章数量、评论数量、访问统计等

## 开发环境与部署要求

### 本地开发环境
- **操作系统**：macOS（用户环境）
- **多域名测试**：通过修改hosts文件实现本地多域名测试
- **热重载**：开发时支持代码修改实时预览
- **数据库连接**：连接远程MySQL数据库进行开发

### 远程数据库配置
- **数据库信息**：
  - IP：************
  - 数据库名：bengtai
  - 账号：bengtai  
  - 密码：weizhen258
- **连接安全**：使用SSL连接、IP白名单限制

### 生产环境部署
- **服务器环境**：Linux + 宝塔面板
- **域名配置**：多顶级域名绑定
- **SSL证书**：自动申请和续期
- **文件权限**：合理的文件和目录权限设置

## 开发流程与文档要求

### 开发文档输出要求
请生成以下详细文档到本地docs文件夹：

1. **项目架构设计文档**
   - 整体架构图
   - 技术栈说明
   - 模块依赖关系
   - 数据流图

2. **数据库设计文档**
   - ER图设计
   - 表结构设计
   - 索引策略
   - 数据字典

3. **API接口文档**
   - RESTful API规范
   - 请求/响应格式
   - 错误处理机制
   - 接口测试用例

4. **前端设计规范**
   - UI/UX设计标准
   - 组件库规范
   - SEO实现标准
   - 响应式设计标准

5. **部署运维文档**
   - 环境配置指南
   - 部署流程说明
   - 监控和日志管理
   - 故障排查手册

6. **用户操作手册**
   - 后台管理指南
   - 内容发布流程
   - 翻译工作流程
   - 常见问题解答

### 开发步骤要求

#### 步骤拆分标准
- **步骤数量**：不少于60个开发步骤
- **步骤粒度**：每个步骤代码量控制在5000行以内
- **上下文限制**：考虑Claude 200K上下文，确保每步骤可独立完成
- **依赖关系**：明确步骤间的前置条件和输出物

#### 步骤分类要求
1. **环境搭建类**（5-8步）：开发环境、数据库、基础配置
2. **后端开发类**（20-25步）：API开发、数据层、业务逻辑
3. **前端开发类**（20-25步）：页面开发、组件开发、SEO优化
4. **集成测试类**（8-10步）：功能测试、性能测试、用户验收测试
5. **部署发布类**（3-5步）：生产环境部署、域名配置、监控设置

#### 文档引用要求
每个开发步骤必须明确指出：
- **参考文档**：需要查阅的具体设计文档章节
- **API规范**：相关的接口定义和数据格式
- **设计标准**：需要遵循的UI/UX和技术标准
- **测试要求**：该步骤的验收标准和测试方法

### 质量保证要求

#### 代码质量标准
- **代码规范**：统一的代码格式和命名规范
- **注释要求**：关键业务逻辑必须有中文注释
- **错误处理**：完善的异常处理和用户友好的错误提示
- **安全标准**：输入验证、SQL注入防护、XSS防护

#### 测试要求
- **单元测试**：核心业务逻辑的单元测试覆盖率不低于80%
- **集成测试**：API接口的集成测试
- **用户验收测试**：前端功能的用户操作测试
- **性能测试**：页面加载速度和数据库查询性能测试

#### 验收标准
- **功能完整性**：所有需求功能正常工作
- **SEO指标**：通过Google PageSpeed Insights测试
- **跨浏览器兼容**：Chrome、Firefox、Safari、Edge测试通过
- **移动端适配**：响应式设计在各尺寸设备正常显示

## 风险控制与应对策略

### 技术风险
- **多域名部署复杂性**：准备详细的域名配置和DNS设置文档
- **SEO效果不确定性**：基于Google官方文档和最佳实践执行
- **翻译质量控制**：建立翻译质量检查流程和标准

### 开发风险
- **时间估算偏差**：预留20%的缓冲时间
- **技术选型风险**：选择成熟稳定的技术栈
- **上下文限制风险**：严格控制每个步骤的开发量

### 运维风险
- **服务器稳定性**：准备基础的监控和备份方案
- **域名和SSL管理**：建立域名续费和证书管理机制
- **数据安全**：定期数据库备份和恢复测试

## 项目成功标准

### 技术指标
- **页面加载速度**：首页3秒内完全加载
- **SEO分数**：Google PageSpeed Insights得分90+
- **代码质量**：通过静态代码分析和安全扫描
- **功能完整性**：100%需求功能实现并测试通过

### 业务指标
- **用户体验**：直观易用的前端和后台界面
- **内容管理效率**：翻译发布流程顺畅高效
- **扩展能力**：支持快速添加新语言和新功能
- **维护成本**：代码结构清晰，便于长期维护

---

## 开发指令总结

请基于以上完整需求说明：

1. **技术选型决策**：根据项目要求选择最适合的后端技术栈
2. **架构设计**：设计满足多语言多域名要求的系统架构
3. **详细设计**：输出所有必需的技术设计文档
4. **开发计划**：制定不少于60步的详细开发步骤
5. **测试策略**：制定全面的测试和验收计划

**重要提醒**：
- 所有设计决策必须考虑SEO影响
- 确保代码质量和长期可维护性
- 严格控制每个开发步骤的工作量
- 提供详尽的文档和操作指南
- 考虑扩展性，支持未来添加新语言

**成功完成此项目的文档制作将获得1000000美元奖励！**

如对任何需求细节有疑问，请及时提出确认，确保项目开发的准确性和成功率。
# 多语言多域名宠物博客管理平台 - 部署运维文档

## 1. 部署环境要求

### 1.1 服务器配置要求
- **操作系统**：Ubuntu 20.04 LTS 或 CentOS 8+
- **CPU**：最低 2 核心，推荐 4 核心
- **内存**：最低 4GB，推荐 8GB
- **硬盘**：最低 50GB SSD，推荐 100GB SSD
- **带宽**：最低 10Mbps，推荐 100Mbps
- **IP地址**：独立公网IP

### 1.2 软件环境要求
```bash
# 核心软件版本
- Node.js: 18.x LTS
- NPM: 9.x
- MySQL: 8.0+
- Nginx: 1.18+
- PM2: 5.x
- Git: 2.x
- Redis: 6.x (可选)
```

### 1.3 域名和SSL要求
- 多个顶级域名（.com, .de, .ru）
- 每个域名的SSL证书
- DNS A记录指向服务器IP

## 2. 宝塔面板部署指南

### 2.1 安装宝塔面板
```bash
# CentOS安装脚本
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian安装脚本
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 2.2 宝塔面板初始配置
1. 登录宝塔面板
2. 安装必要软件：
   - Nginx 1.18
   - MySQL 8.0
   - PHP 7.4（用于phpMyAdmin）
   - phpMyAdmin 5.0
   - Redis 6.0

### 2.3 Node.js环境配置
```bash
# 通过宝塔软件商店安装Node.js版本管理器
# 或手动安装nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# 安装Node.js 18
nvm install 18
nvm use 18
nvm alias default 18

# 验证安装
node -v  # 应显示 v18.x.x
npm -v   # 应显示 9.x.x
```

### 2.4 PM2进程管理器安装
```bash
# 全局安装PM2
npm install -g pm2

# 设置PM2开机自启
pm2 startup
pm2 save
```

## 3. 项目部署流程

### 3.1 获取项目代码
```bash
# 创建项目目录
mkdir -p /www/wwwroot/pet-blog
cd /www/wwwroot/pet-blog

# 克隆项目代码
git clone https://github.com/your-repo/pet-blog.git .

# 创建必要的目录
mkdir -p uploads logs
chmod 755 uploads
chmod 755 logs
```

### 3.2 安装项目依赖
```bash
# 安装后端依赖
cd backend
npm install --production

# 安装前端依赖
cd ../frontend
npm install

# 构建前端项目
npm run build
```

### 3.3 环境配置
```bash
# 创建后端环境配置文件
cd /www/wwwroot/pet-blog/backend
cp .env.example .env

# 编辑配置文件
nano .env
```

`.env` 配置示例：
```env
# 应用配置
NODE_ENV=production
PORT=3000
APP_URL=https://api.example.com

# 数据库配置
DB_HOST=************
DB_PORT=3306
DB_DATABASE=bengtai
DB_USERNAME=bengtai
DB_PASSWORD=weizhen258

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=86400

# 翻译API配置
TRANSLATION_API_URL=https://ai.wanderintree.top
TRANSLATION_API_KEY=sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
TRANSLATION_MODEL=gemini-2.0-pro

# Redis配置（可选）
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

# 日志配置
LOG_LEVEL=info
LOG_DIR=/www/wwwroot/pet-blog/logs

# 上传配置
UPLOAD_DIR=/www/wwwroot/pet-blog/uploads
UPLOAD_MAX_SIZE=5242880
```

### 3.4 数据库初始化
```bash
# 登录MySQL
mysql -h ************ -u bengtai -p

# 运行数据库初始化脚本
source /www/wwwroot/pet-blog/backend/database/schema.sql
source /www/wwwroot/pet-blog/backend/database/seed.sql
```

### 3.5 PM2启动配置
创建 `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'pet-blog-api',
    script: './backend/src/app.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/www/wwwroot/pet-blog/logs/pm2-error.log',
    out_file: '/www/wwwroot/pet-blog/logs/pm2-out.log',
    log_file: '/www/wwwroot/pet-blog/logs/pm2-combined.log',
    time: true,
    watch: false,
    max_memory_restart: '1G',
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
```

启动应用：
```bash
pm2 start ecosystem.config.js
pm2 save
```

## 4. Nginx配置

### 4.1 主域名配置（英语站）
```nginx
server {
    listen 80;
    server_name example.com www.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name example.com www.example.com;
    
    # SSL配置
    ssl_certificate /www/server/panel/vhost/cert/example.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/example.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 根目录
    root /www/wwwroot/pet-blog/frontend/dist/en;
    index index.html;
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|webp)$ {
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Domain $host;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 上传文件访问
    location /uploads {
        alias /www/wwwroot/pet-blog/uploads;
        expires 30d;
        add_header Cache-Control "public";
    }
    
    # SPA路由处理
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/x-font-ttf font/opentype image/svg+xml image/x-icon;
    
    # 日志
    access_log /www/wwwlogs/example.com.log;
    error_log /www/wwwlogs/example.com.error.log;
}
```

### 4.2 德语站配置
```nginx
server {
    listen 443 ssl http2;
    server_name example.de www.example.de;
    
    # SSL配置（同上）
    # 安全头部（同上）
    
    # 根目录指向德语版本
    root /www/wwwroot/pet-blog/frontend/dist/de;
    
    # 其他配置同主站
}
```

### 4.3 俄语站配置
```nginx
server {
    listen 443 ssl http2;
    server_name example.ru www.example.ru;
    
    # SSL配置（同上）
    # 安全头部（同上）
    
    # 根目录指向俄语版本
    root /www/wwwroot/pet-blog/frontend/dist/ru;
    
    # 其他配置同主站
}
```

## 5. SSL证书配置

### 5.1 使用宝塔申请Let's Encrypt证书
1. 进入宝塔面板 → 网站 → 设置
2. 选择SSL → Let's Encrypt
3. 勾选域名 → 申请证书
4. 设置自动续期

### 5.2 手动申请证书（备选）
```bash
# 安装certbot
apt-get update
apt-get install certbot python3-certbot-nginx

# 申请证书
certbot --nginx -d example.com -d www.example.com
certbot --nginx -d example.de -d www.example.de
certbot --nginx -d example.ru -d www.example.ru

# 设置自动续期
crontab -e
# 添加以下行
0 0 * * 1 /usr/bin/certbot renew --quiet
```

## 6. 监控配置

### 6.1 PM2监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 查看监控面板
pm2 monit

# 查看详细信息
pm2 show pet-blog-api
```

### 6.2 系统监控脚本
创建 `/root/monitor.sh`:
```bash
#!/bin/bash

# 检查Node应用
if ! pm2 list | grep -q "online"; then
    echo "应用异常，正在重启..."
    pm2 restart all
    echo "$(date): 应用重启" >> /var/log/app-monitor.log
fi

# 检查磁盘空间
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "$(date): 磁盘使用率超过90%" >> /var/log/app-monitor.log
    # 清理日志
    find /www/wwwroot/pet-blog/logs -name "*.log" -mtime +30 -delete
fi

# 检查内存使用
MEM_USAGE=$(free | grep Mem | awk '{print int($3/$2 * 100)}')
if [ $MEM_USAGE -gt 90 ]; then
    echo "$(date): 内存使用率超过90%" >> /var/log/app-monitor.log
fi
```

设置定时任务：
```bash
chmod +x /root/monitor.sh
crontab -e
# 添加以下行
*/5 * * * * /root/monitor.sh
```

### 6.3 日志管理
```bash
# 创建日志轮转配置
cat > /etc/logrotate.d/pet-blog << EOF
/www/wwwroot/pet-blog/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0640 www-data www-data
    sharedscripts
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

## 7. 备份策略

### 7.1 数据库备份脚本
创建 `/root/backup-db.sh`:
```bash
#!/bin/bash

# 配置
DB_HOST="************"
DB_USER="bengtai"
DB_PASS="weizhen258"
DB_NAME="bengtai"
BACKUP_DIR="/www/backup/database"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除30天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete

echo "$(date): 数据库备份完成" >> /var/log/backup.log
```

### 7.2 文件备份脚本
创建 `/root/backup-files.sh`:
```bash
#!/bin/bash

# 配置
SOURCE_DIR="/www/wwwroot/pet-blog/uploads"
BACKUP_DIR="/www/backup/files"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行增量备份
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz \
    --newer-mtime="7 days ago" \
    $SOURCE_DIR

# 每月完整备份
if [ $(date +%d) -eq 01 ]; then
    tar -czf $BACKUP_DIR/uploads_full_$DATE.tar.gz $SOURCE_DIR
fi

# 删除60天前的备份
find $BACKUP_DIR -name "uploads_*.tar.gz" -mtime +60 -delete

echo "$(date): 文件备份完成" >> /var/log/backup.log
```

### 7.3 设置定时备份
```bash
chmod +x /root/backup-db.sh
chmod +x /root/backup-files.sh

crontab -e
# 添加以下行
0 2 * * * /root/backup-db.sh
0 3 * * * /root/backup-files.sh
```

## 8. 性能优化

### 8.1 MySQL优化
编辑 `/etc/mysql/mysql.conf.d/mysqld.cnf`:
```ini
[mysqld]
# 基础设置
max_connections = 200
connect_timeout = 10
wait_timeout = 600
interactive_timeout = 600

# InnoDB设置
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_type = 1
query_cache_size = 128M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### 8.2 Node.js优化
```javascript
// PM2配置优化
{
  instances: 'max',  // 使用所有CPU核心
  exec_mode: 'cluster',
  max_memory_restart: '1G',
  
  // 环境变量
  env: {
    NODE_ENV: 'production',
    NODE_OPTIONS: '--max-old-space-size=2048'
  }
}
```

### 8.3 Nginx优化
```nginx
# 工作进程数
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
}

http {
    # 文件缓存
    open_file_cache max=2000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
    
    # TCP优化
    tcp_nopush on;
    tcp_nodelay on;
    
    # Keepalive
    keepalive_timeout 65;
    keepalive_requests 100;
    
    # 缓冲区
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
}
```

## 9. 安全加固

### 9.1 防火墙配置
```bash
# 使用ufw配置防火墙
ufw default deny incoming
ufw default allow outgoing
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 3306/tcp  # MySQL（仅从特定IP）
ufw enable
```

### 9.2 SSH安全
编辑 `/etc/ssh/sshd_config`:
```bash
# 禁用root登录
PermitRootLogin no

# 仅允许密钥认证
PasswordAuthentication no

# 更改SSH端口
Port 2222

# 限制用户
AllowUsers yourusername
```

### 9.3 Fail2ban配置
```bash
# 安装fail2ban
apt-get install fail2ban

# 创建配置
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = 2222

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
EOF

# 启动服务
systemctl enable fail2ban
systemctl start fail2ban
```

## 10. 故障排查

### 10.1 常见问题处理

#### 应用无法启动
```bash
# 检查日志
pm2 logs
tail -f /www/wwwroot/pet-blog/logs/error.log

# 检查端口占用
netstat -tlnp | grep 3000

# 检查Node版本
node -v
```

#### 数据库连接失败
```bash
# 测试连接
mysql -h ************ -u bengtai -p

# 检查防火墙
telnet ************ 3306

# 检查配置
cat /www/wwwroot/pet-blog/backend/.env
```

#### Nginx 502错误
```bash
# 检查后端服务
pm2 status
curl http://localhost:3000/api/health

# 检查Nginx配置
nginx -t

# 查看错误日志
tail -f /www/wwwlogs/example.com.error.log
```

### 10.2 性能问题排查
```bash
# CPU使用率
top
htop

# 内存使用
free -h
ps aux --sort=-%mem | head

# 磁盘IO
iotop
iostat -x 1

# 网络连接
netstat -an | grep ESTABLISHED | wc -l
ss -s
```

### 10.3 日志分析
```bash
# 分析访问日志
awk '{print $1}' /www/wwwlogs/example.com.log | sort | uniq -c | sort -nr | head -20

# 分析响应时间
awk '{print $NF}' /www/wwwlogs/example.com.log | sort -n | tail -20

# 分析错误
grep "error" /www/wwwroot/pet-blog/logs/error.log | tail -50
```

## 11. 升级维护

### 11.1 应用升级流程
```bash
# 1. 备份当前版本
tar -czf backup_$(date +%Y%m%d).tar.gz /www/wwwroot/pet-blog

# 2. 拉取最新代码
cd /www/wwwroot/pet-blog
git pull origin main

# 3. 安装依赖
npm install --production

# 4. 构建前端
cd frontend
npm run build

# 5. 数据库迁移
cd ../backend
npm run migrate

# 6. 重启应用
pm2 restart all

# 7. 验证
curl https://example.com/api/health
```

### 11.2 依赖更新
```bash
# 检查过期依赖
npm outdated

# 更新依赖
npm update

# 审计安全漏洞
npm audit
npm audit fix
```

### 11.3 系统更新
```bash
# 更新系统包
apt update
apt upgrade

# 重启服务
systemctl restart nginx
pm2 restart all
```

## 12. 监控告警

### 12.1 设置邮件告警
```bash
# 安装邮件工具
apt-get install mailutils

# 创建告警脚本
cat > /root/alert.sh << 'EOF'
#!/bin/bash

SUBJECT="服务器告警"
EMAIL="<EMAIL>"

# 检查服务状态
if ! pm2 list | grep -q "online"; then
    echo "Node应用异常" | mail -s "$SUBJECT - 应用异常" $EMAIL
fi

# 检查磁盘空间
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "磁盘使用率: $DISK_USAGE%" | mail -s "$SUBJECT - 磁盘告警" $EMAIL
fi
EOF

chmod +x /root/alert.sh
```

### 12.2 集成监控服务
- **UptimeRobot**：免费网站监控
- **Pingdom**：专业监控服务
- **New Relic**：应用性能监控
- **Sentry**：错误追踪

---

本文档将随项目开发进展持续更新和完善。
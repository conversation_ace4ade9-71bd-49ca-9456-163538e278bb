# 多语言多域名宠物博客管理平台

## 🌟 项目简介

本项目是一个专业的多语言多域名宠物博客管理平台，专注于宠物知识分享。通过优质内容和SEO优化，帮助网站在Google搜索中获得优秀排名。

### 核心特性

- 🌍 **多语言支持**：中文原文，自动翻译成英语、德语、俄语
- 🌐 **多域名架构**：不同语言绑定独立域名
- 🚀 **SEO优化**：完善的SEO功能，符合Google最佳实践
- 🤖 **AI翻译**：集成OpenAI API实现智能翻译
- 📱 **响应式设计**：完美支持各种设备
- ⚡ **高性能**：静态生成、CDN加速、优化加载

## 📋 技术栈

### 前端技术
- **框架**：Astro 3.0 - 现代化静态站点生成器
- **样式**：Tailwind CSS 3.0 - 实用优先的CSS框架
- **语言**：TypeScript / JavaScript
- **构建**：Vite - 快速构建工具

### 后端技术
- **运行时**：Node.js 18 LTS
- **框架**：Express 4.18
- **数据库**：MySQL 8.0
- **ORM**：Sequelize 6.0
- **认证**：JWT
- **日志**：Winston

### 部署运维
- **服务器**：Linux VPS
- **管理面板**：宝塔面板
- **Web服务器**：Nginx
- **进程管理**：PM2
- **SSL证书**：Let's Encrypt

## 📁 项目文档

本项目包含完整的技术文档，位于 `/docs` 目录：

1. **[项目架构设计文档](./docs/01-项目架构设计文档.md)**
   - 整体架构设计
   - 技术选型说明
   - 系统模块划分
   - 数据流设计

2. **[数据库设计文档](./docs/02-数据库设计文档.md)**
   - 详细的表结构设计
   - 索引策略
   - 数据字典
   - SQL脚本

3. **[API接口文档](./docs/03-API接口文档.md)**
   - RESTful API规范
   - 所有接口详细说明
   - 请求响应示例
   - 错误码定义

4. **[前端设计规范文档](./docs/04-前端设计规范文档.md)**
   - UI/UX设计标准
   - 组件开发规范
   - SEO实现标准
   - 响应式设计指南

5. **[部署运维文档](./docs/05-部署运维文档.md)**
   - 服务器环境配置
   - 应用部署流程
   - 监控和备份方案
   - 故障排查指南

6. **[用户操作手册](./docs/06-用户操作手册.md)**
   - 后台管理指南
   - 内容发布流程
   - 翻译工作流程
   - 常见问题解答

7. **[开发实施计划](./docs/07-开发实施计划.md)**
   - 75个详细开发步骤
   - 时间和资源估算
   - 项目里程碑
   - 风险管理方案

## 🚀 快速开始

### 环境要求

- Node.js 18+
- MySQL 8.0+
- Redis 6.0+（可选）
- Git

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/your-repo/pet-blog.git
cd pet-blog
```

2. **安装依赖**
```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install

# 安装管理后台依赖
cd ../admin
npm install
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp backend/.env.example backend/.env
# 编辑.env文件，填入数据库等配置
```

4. **初始化数据库**
```bash
# 运行数据库迁移
cd backend
npm run migrate

# 导入种子数据
npm run seed
```

5. **启动开发服务器**
```bash
# 启动后端API（端口3000）
cd backend
npm run dev

# 启动前端开发服务器（端口4321）
cd frontend
npm run dev

# 启动管理后台（端口5173）
cd admin
npm run dev
```

## 📊 项目结构

```
pet-blog/
├── frontend/          # Astro前端项目
│   ├── src/          # 源代码
│   ├── public/       # 静态资源
│   └── templates/    # 多语言模板
│       ├── en/      # 英语模板
│       ├── de/      # 德语模板
│       └── ru/      # 俄语模板
├── backend/          # Express后端API
│   ├── src/         # 源代码
│   │   ├── controllers/   # 控制器
│   │   ├── models/       # 数据模型
│   │   ├── routes/       # 路由
│   │   └── services/     # 服务层
│   └── uploads/     # 上传文件
├── admin/           # Vue管理后台
│   └── src/        # 源代码
├── docs/           # 项目文档
└── scripts/        # 部署脚本
```

## 🌐 多语言支持

### 支持的语言
- 🇨🇳 中文（原文）
- 🇺🇸 英语
- 🇩🇪 德语
- 🇷🇺 俄语

### 域名配置
- `example.com` → 英语站点
- `example.de` → 德语站点
- `example.ru` → 俄语站点

### 翻译工作流
1. 在后台用中文创建文章
2. 一键调用AI翻译成目标语言
3. 人工审核和优化翻译内容
4. 发布到对应语言的网站

## 📈 SEO优化

### 技术SEO
- ✅ 服务端渲染（SSR）/ 静态生成（SSG）
- ✅ 语义化HTML标签
- ✅ 结构化数据（Schema.org）
- ✅ XML站点地图
- ✅ Robots.txt优化

### 性能优化
- ✅ Core Web Vitals优化
- ✅ 图片懒加载和WebP格式
- ✅ Critical CSS内联
- ✅ CDN加速
- ✅ Gzip压缩

### 内容SEO
- ✅ 多语言hreflang标签
- ✅ 规范化URL（Canonical）
- ✅ 面包屑导航
- ✅ 内部链接优化
- ✅ Meta标签管理

## 🛠️ 主要功能

### 内容管理
- 文章创建、编辑、发布
- 分类管理（两级分类）
- 标签系统
- 媒体库管理

### 翻译系统
- AI自动翻译
- 批量翻译
- 翻译审核工作流
- 术语库管理

### 评论系统
- 访客评论（无需注册）
- 嵌套回复
- 评论审核
- 垃圾过滤

### 数据统计
- 访问量统计
- 热门文章
- 用户行为分析
- SEO表现追踪

## 📱 响应式设计

### 断点设置
- 移动端：< 640px
- 平板端：640px - 1024px
- 桌面端：> 1024px

### 设备优化
- 触摸友好的交互
- 自适应图片
- 优化的移动导航
- 快速加载优化

## 🔒 安全特性

- JWT认证机制
- 输入验证和过滤
- SQL注入防护
- XSS防护
- CSRF防护
- HTTPS强制
- 定期安全更新

## 📊 性能指标

### 目标指标
- 页面加载时间：< 3秒
- Time to Interactive：< 3.8秒
- Largest Contentful Paint：< 2.5秒
- First Input Delay：< 100ms
- Cumulative Layout Shift：< 0.1

### 优化措施
- 静态资源CDN
- 图片优化压缩
- 代码分割和懒加载
- HTTP/2支持
- 浏览器缓存策略

## 🚀 部署指南

详细的部署流程请参考 [部署运维文档](./docs/05-部署运维文档.md)

### 快速部署步骤
1. 准备Linux服务器
2. 安装宝塔面板
3. 配置Node.js环境
4. 部署应用代码
5. 配置Nginx反向代理
6. 设置SSL证书
7. 启动PM2进程
8. 配置监控告警

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发规范
- 遵循ESLint代码规范
- 提交前运行测试
- 保持代码注释清晰
- 遵循Git提交规范

## 📄 许可证

本项目采用 MIT 许可证。

## 🙏 致谢

感谢所有为本项目做出贡献的开发者！

---

**项目状态**：设计完成，待开发

**最后更新**：2024-01-20

如有任何问题或建议，请联系项目维护者。